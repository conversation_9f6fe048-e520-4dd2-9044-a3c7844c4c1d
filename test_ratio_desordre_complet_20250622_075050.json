{"partie_test": {"partie_number": "RATIO_TEST_001", "mains": [{"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_B_PLAYER"}, {"index5_combined": "0_C_TIE"}, {"index5_combined": "1_A_BANKER"}, {"index5_combined": "1_B_PLAYER"}, {"index5_combined": "1_C_TIE"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "0_B_PLAYER"}, {"index5_combined": "0_B_PLAYER"}, {"index5_combined": "0_A_BANKER"}, {"index5_combined": "1_C_TIE"}, {"index5_combined": "0_B_PLAYER"}, {"index5_combined": "1_A_BANKER"}, {"index5_combined": "0_C_TIE"}, {"index5_combined": "1_B_PLAYER"}]}, "entropies_globales": {"partie_id": "RATIO_TEST_001", "nb_mains_total": 20, "entropies_globales": {"1": {"entropie_globale": -0.0, "longueur_sequence": 1, "nb_valeurs_distinctes": 1, "distribution_pattern": [1], "entropie_max_theorique": 0.0, "pourcentage_entropie_max": 0, "sequence_globale": ["0_A_BANKER"]}, "2": {"entropie_globale": -0.0, "longueur_sequence": 2, "nb_valeurs_distinctes": 1, "distribution_pattern": [2], "entropie_max_theorique": 1.0, "pourcentage_entropie_max": -0.0, "sequence_globale": ["0_A_BANKER", "0_A_BANKER"]}, "3": {"entropie_globale": -0.0, "longueur_sequence": 3, "nb_valeurs_distinctes": 1, "distribution_pattern": [3], "entropie_max_theorique": 1.585, "pourcentage_entropie_max": -0.0, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER"]}, "4": {"entropie_globale": -0.0, "longueur_sequence": 4, "nb_valeurs_distinctes": 1, "distribution_pattern": [4], "entropie_max_theorique": 2.0, "pourcentage_entropie_max": -0.0, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER"]}, "5": {"entropie_globale": -0.0, "longueur_sequence": 5, "nb_valeurs_distinctes": 1, "distribution_pattern": [5], "entropie_max_theorique": 2.3219, "pourcentage_entropie_max": -0.0, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER"]}, "6": {"entropie_globale": 0.65, "longueur_sequence": 6, "nb_valeurs_distinctes": 2, "distribution_pattern": [5, 1], "entropie_max_theorique": 2.585, "pourcentage_entropie_max": 25.1, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER"]}, "7": {"entropie_globale": 1.1488, "longueur_sequence": 7, "nb_valeurs_distinctes": 3, "distribution_pattern": [5, 1, 1], "entropie_max_theorique": 2.8074, "pourcentage_entropie_max": 40.9, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE"]}, "8": {"entropie_globale": 1.5488, "longueur_sequence": 8, "nb_valeurs_distinctes": 4, "distribution_pattern": [5, 1, 1, 1], "entropie_max_theorique": 3.0, "pourcentage_entropie_max": 51.6, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER"]}, "9": {"entropie_globale": 1.88, "longueur_sequence": 9, "nb_valeurs_distinctes": 5, "distribution_pattern": [5, 1, 1, 1, 1], "entropie_max_theorique": 3.1699, "pourcentage_entropie_max": 59.3, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER"]}, "10": {"entropie_globale": 2.161, "longueur_sequence": 10, "nb_valeurs_distinctes": 6, "distribution_pattern": [5, 1, 1, 1, 1, 1], "entropie_max_theorique": 3.3219, "pourcentage_entropie_max": 65.1, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE"]}, "11": {"entropie_globale": 2.0495, "longueur_sequence": 11, "nb_valeurs_distinctes": 6, "distribution_pattern": [6, 1, 1, 1, 1, 1], "entropie_max_theorique": 3.4594, "pourcentage_entropie_max": 59.2, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER"]}, "12": {"entropie_globale": 1.9473, "longueur_sequence": 12, "nb_valeurs_distinctes": 6, "distribution_pattern": [7, 1, 1, 1, 1, 1], "entropie_max_theorique": 3.585, "pourcentage_entropie_max": 54.3, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER"]}, "13": {"entropie_globale": 2.0349, "longueur_sequence": 13, "nb_valeurs_distinctes": 6, "distribution_pattern": [7, 2, 1, 1, 1, 1], "entropie_max_theorique": 3.7004, "pourcentage_entropie_max": 55.0, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER"]}, "14": {"entropie_globale": 2.064, "longueur_sequence": 14, "nb_valeurs_distinctes": 6, "distribution_pattern": [7, 3, 1, 1, 1, 1], "entropie_max_theorique": 3.8074, "pourcentage_entropie_max": 54.2, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER"]}, "15": {"entropie_globale": 1.9899, "longueur_sequence": 15, "nb_valeurs_distinctes": 6, "distribution_pattern": [8, 3, 1, 1, 1, 1], "entropie_max_theorique": 3.9069, "pourcentage_entropie_max": 50.9, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER"]}, "16": {"entropie_globale": 2.0778, "longueur_sequence": 16, "nb_valeurs_distinctes": 6, "distribution_pattern": [8, 3, 2, 1, 1, 1], "entropie_max_theorique": 4.0, "pourcentage_entropie_max": 51.9, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE"]}, "17": {"entropie_globale": 2.0875, "longueur_sequence": 17, "nb_valeurs_distinctes": 6, "distribution_pattern": [8, 4, 2, 1, 1, 1], "entropie_max_theorique": 4.0875, "pourcentage_entropie_max": 51.1, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER"]}, "18": {"entropie_globale": 2.1699, "longueur_sequence": 18, "nb_valeurs_distinctes": 6, "distribution_pattern": [8, 4, 2, 2, 1, 1], "entropie_max_theorique": 4.1699, "pourcentage_entropie_max": 52.0, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER"]}, "19": {"entropie_globale": 2.2479, "longueur_sequence": 19, "nb_valeurs_distinctes": 6, "distribution_pattern": [8, 4, 2, 2, 2, 1], "entropie_max_theorique": 4.1699, "pourcentage_entropie_max": 53.9, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE"]}, "20": {"entropie_globale": 2.3219, "longueur_sequence": 20, "nb_valeurs_distinctes": 6, "distribution_pattern": [8, 4, 2, 2, 2, 2], "entropie_max_theorique": 4.1699, "pourcentage_entropie_max": 55.7, "sequence_globale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE", "1_B_PLAYER"]}}, "analyse_convergence": {"entropie_finale": 2.3219, "entropie_theorique_limite": 4.1699, "convergence_vers_limite": "False", "pourcentage_convergence": 55.7, "tendance_finale": 0.032662}, "performance": {"temps_calcul_secondes": 0.001, "mains_par_seconde": 13925.3}}, "signatures_locales": {"5": {"sequence": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER"], "longueur": 5, "entropie_shannon": -0.0, "distribution_pattern": [5], "nb_valeurs_distinctes": 1, "dominance_ratio": "INF", "uniformite_score": 1.0, "classe_predictive": "DÉTERMINISTE", "potentiel_predictif": 100.0, "valeurs_uniques": ["0_A_BANKER"], "occurrences": {"0_A_BANKER": 5}}, "6": {"sequence": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER"], "longueur": 5, "entropie_shannon": 0.7219, "distribution_pattern": [4, 1], "nb_valeurs_distinctes": 2, "dominance_ratio": 4.0, "uniformite_score": 0.7, "classe_predictive": "TRÈS_PRÉDICTIBLE", "potentiel_predictif": 68.9, "valeurs_uniques": ["0_A_BANKER", "0_B_PLAYER"], "occurrences": {"0_A_BANKER": 4, "0_B_PLAYER": 1}}, "7": {"sequence": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE"], "longueur": 5, "entropie_shannon": 1.371, "distribution_pattern": [3, 1, 1], "nb_valeurs_distinctes": 3, "dominance_ratio": 3.0, "uniformite_score": 0.811, "classe_predictive": "PEU_PRÉDICTIBLE", "potentiel_predictif": 41.0, "valeurs_uniques": ["0_A_BANKER", "0_B_PLAYER", "0_C_TIE"], "occurrences": {"0_A_BANKER": 3, "0_B_PLAYER": 1, "0_C_TIE": 1}}, "8": {"sequence": ["0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER"], "longueur": 5, "entropie_shannon": 1.9219, "distribution_pattern": [2, 1, 1, 1], "nb_valeurs_distinctes": 4, "dominance_ratio": 2.0, "uniformite_score": 0.913, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 17.2, "valeurs_uniques": ["0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER"], "occurrences": {"0_A_BANKER": 2, "0_B_PLAYER": 1, "0_C_TIE": 1, "1_A_BANKER": 1}}, "9": {"sequence": ["0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER"], "longueur": 5, "entropie_shannon": 2.3219, "distribution_pattern": [1, 1, 1, 1, 1], "nb_valeurs_distinctes": 5, "dominance_ratio": 1.0, "uniformite_score": 1.0, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 0.0, "valeurs_uniques": ["0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER"], "occurrences": {"0_A_BANKER": 1, "0_B_PLAYER": 1, "0_C_TIE": 1, "1_A_BANKER": 1, "1_B_PLAYER": 1}}, "10": {"sequence": ["0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE"], "longueur": 5, "entropie_shannon": 2.3219, "distribution_pattern": [1, 1, 1, 1, 1], "nb_valeurs_distinctes": 5, "dominance_ratio": 1.0, "uniformite_score": 1.0, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 0.0, "valeurs_uniques": ["0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE"], "occurrences": {"0_B_PLAYER": 1, "0_C_TIE": 1, "1_A_BANKER": 1, "1_B_PLAYER": 1, "1_C_TIE": 1}}, "11": {"sequence": ["0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER"], "longueur": 5, "entropie_shannon": 2.3219, "distribution_pattern": [1, 1, 1, 1, 1], "nb_valeurs_distinctes": 5, "dominance_ratio": 1.0, "uniformite_score": 1.0, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 0.0, "valeurs_uniques": ["0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER"], "occurrences": {"0_C_TIE": 1, "1_A_BANKER": 1, "1_B_PLAYER": 1, "1_C_TIE": 1, "0_A_BANKER": 1}}, "12": {"sequence": ["1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER"], "longueur": 5, "entropie_shannon": 1.9219, "distribution_pattern": [2, 1, 1, 1], "nb_valeurs_distinctes": 4, "dominance_ratio": 2.0, "uniformite_score": 0.913, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 17.2, "valeurs_uniques": ["1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER"], "occurrences": {"1_A_BANKER": 1, "1_B_PLAYER": 1, "1_C_TIE": 1, "0_A_BANKER": 2}}, "13": {"sequence": ["1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER"], "longueur": 5, "entropie_shannon": 1.9219, "distribution_pattern": [2, 1, 1, 1], "nb_valeurs_distinctes": 4, "dominance_ratio": 2.0, "uniformite_score": 0.913, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 17.2, "valeurs_uniques": ["1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_B_PLAYER"], "occurrences": {"1_B_PLAYER": 1, "1_C_TIE": 1, "0_A_BANKER": 2, "0_B_PLAYER": 1}}, "14": {"sequence": ["1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER"], "longueur": 5, "entropie_shannon": 1.5219, "distribution_pattern": [2, 2, 1], "nb_valeurs_distinctes": 3, "dominance_ratio": 2.0, "uniformite_score": 0.906, "classe_predictive": "PEU_PRÉDICTIBLE", "potentiel_predictif": 34.5, "valeurs_uniques": ["1_C_TIE", "0_A_BANKER", "0_B_PLAYER"], "occurrences": {"1_C_TIE": 1, "0_A_BANKER": 2, "0_B_PLAYER": 2}}, "15": {"sequence": ["0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER"], "longueur": 5, "entropie_shannon": 0.971, "distribution_pattern": [3, 2], "nb_valeurs_distinctes": 2, "dominance_ratio": 1.5, "uniformite_score": 0.9, "classe_predictive": "MODÉRÉMENT_PRÉDICTIBLE", "potentiel_predictif": 58.2, "valeurs_uniques": ["0_A_BANKER", "0_B_PLAYER"], "occurrences": {"0_A_BANKER": 3, "0_B_PLAYER": 2}}, "16": {"sequence": ["0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE"], "longueur": 5, "entropie_shannon": 1.5219, "distribution_pattern": [2, 2, 1], "nb_valeurs_distinctes": 3, "dominance_ratio": 2.0, "uniformite_score": 0.906, "classe_predictive": "PEU_PRÉDICTIBLE", "potentiel_predictif": 34.5, "valeurs_uniques": ["0_A_BANKER", "0_B_PLAYER", "1_C_TIE"], "occurrences": {"0_A_BANKER": 2, "0_B_PLAYER": 2, "1_C_TIE": 1}}, "17": {"sequence": ["0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER"], "longueur": 5, "entropie_shannon": 1.371, "distribution_pattern": [3, 1, 1], "nb_valeurs_distinctes": 3, "dominance_ratio": 3.0, "uniformite_score": 0.811, "classe_predictive": "PEU_PRÉDICTIBLE", "potentiel_predictif": 41.0, "valeurs_uniques": ["0_B_PLAYER", "0_A_BANKER", "1_C_TIE"], "occurrences": {"0_B_PLAYER": 3, "0_A_BANKER": 1, "1_C_TIE": 1}}, "18": {"sequence": ["0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER"], "longueur": 5, "entropie_shannon": 1.9219, "distribution_pattern": [2, 1, 1, 1], "nb_valeurs_distinctes": 4, "dominance_ratio": 2.0, "uniformite_score": 0.913, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 17.2, "valeurs_uniques": ["0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "1_A_BANKER"], "occurrences": {"0_B_PLAYER": 2, "0_A_BANKER": 1, "1_C_TIE": 1, "1_A_BANKER": 1}}, "19": {"sequence": ["0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE"], "longueur": 5, "entropie_shannon": 2.3219, "distribution_pattern": [1, 1, 1, 1, 1], "nb_valeurs_distinctes": 5, "dominance_ratio": 1.0, "uniformite_score": 1.0, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 0.0, "valeurs_uniques": ["0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE"], "occurrences": {"0_A_BANKER": 1, "1_C_TIE": 1, "0_B_PLAYER": 1, "1_A_BANKER": 1, "0_C_TIE": 1}}, "20": {"sequence": ["1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE", "1_B_PLAYER"], "longueur": 5, "entropie_shannon": 2.3219, "distribution_pattern": [1, 1, 1, 1, 1], "nb_valeurs_distinctes": 5, "dominance_ratio": 1.0, "uniformite_score": 1.0, "classe_predictive": "QUASI_ALÉATOIRE", "potentiel_predictif": 0.0, "valeurs_uniques": ["1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE", "1_B_PLAYER"], "occurrences": {"1_C_TIE": 1, "0_B_PLAYER": 1, "1_A_BANKER": 1, "0_C_TIE": 1, "1_B_PLAYER": 1}}}, "ratios_desordre": {"5": {"entropie_locale": -0.0, "entropie_globale": -0.0, "ratio_desordre": Infinity, "prediction": {"tendance": "VERS_MOINS_DE_DÉSORDRE", "intensité": "FORTE", "confiance": 95, "explication": "Chaos local excessif → Correction vers ordre"}, "sequence_locale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER"]}, "6": {"entropie_locale": 0.7219, "entropie_globale": 0.65, "ratio_desordre": 1.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER"]}, "7": {"entropie_locale": 1.371, "entropie_globale": 1.1488, "ratio_desordre": 1.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE"]}, "8": {"entropie_locale": 1.9219, "entropie_globale": 1.5488, "ratio_desordre": 1.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER"]}, "9": {"entropie_locale": 2.3219, "entropie_globale": 1.88, "ratio_desordre": 1.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER"]}, "10": {"entropie_locale": 2.3219, "entropie_globale": 2.161, "ratio_desordre": 1.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_B_PLAYER", "0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE"]}, "11": {"entropie_locale": 2.3219, "entropie_globale": 2.0495, "ratio_desordre": 1.***************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_C_TIE", "1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER"]}, "12": {"entropie_locale": 1.9219, "entropie_globale": 1.9473, "ratio_desordre": 0.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["1_A_BANKER", "1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER"]}, "13": {"entropie_locale": 1.9219, "entropie_globale": 2.0349, "ratio_desordre": 0.***************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["1_B_PLAYER", "1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER"]}, "14": {"entropie_locale": 1.5219, "entropie_globale": 2.064, "ratio_desordre": 0.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["1_C_TIE", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER"]}, "15": {"entropie_locale": 0.971, "entropie_globale": 1.9899, "ratio_desordre": 0.****************, "prediction": {"tendance": "VERS_PLUS_DE_DÉSORDRE", "intensité": "FORTE", "confiance": 21.**************, "explication": "Ordre local excessif → Correction vers chaos"}, "sequence_locale": ["0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER"]}, "16": {"entropie_locale": 1.5219, "entropie_globale": 2.0778, "ratio_desordre": 0.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE"]}, "17": {"entropie_locale": 1.371, "entropie_globale": 2.0875, "ratio_desordre": 0.****************, "prediction": {"tendance": "VERS_PLUS_DE_DÉSORDRE", "intensité": "MODÉRÉE", "confiance": 4.***************, "explication": "Ordre local excessif → Correction vers chaos"}, "sequence_locale": ["0_B_PLAYER", "0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER"]}, "18": {"entropie_locale": 1.9219, "entropie_globale": 2.1699, "ratio_desordre": 0.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_B_PLAYER", "0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER"]}, "19": {"entropie_locale": 2.3219, "entropie_globale": 2.2479, "ratio_desordre": 1.****************, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["0_A_BANKER", "1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE"]}, "20": {"entropie_locale": 2.3219, "entropie_globale": 2.3219, "ratio_desordre": 1.0, "prediction": {"tendance": "MAINTIEN_ÉQUILIBRE", "intensité": "STABLE", "confiance": 70, "explication": "Cohérence locale/globale → Continuation"}, "sequence_locale": ["1_C_TIE", "0_B_PLAYER", "1_A_BANKER", "0_C_TIE", "1_B_PLAYER"]}}, "synthese_predictions": {"VERS_MOINS_DE_DÉSORDRE": 1, "MAINTIEN_ÉQUILIBRE": 13, "VERS_PLUS_DE_DÉSORDRE": 2}}