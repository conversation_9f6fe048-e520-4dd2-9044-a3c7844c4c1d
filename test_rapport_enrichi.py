#!/usr/bin/env python3
"""
TEST SPÉCIFIQUE DU RAPPORT ENRICHI
==================================

Test pour générer et examiner le rapport TXT enrichi avec toutes les
informations détaillées sur les ratios d'entropie.
"""

import json
import os
from datetime import datetime


def creer_dataset_test_enrichi():
    """Crée un dataset de test plus riche pour le rapport"""
    
    dataset_test = {
        "metadata": {
            "version": "1.0",
            "created": datetime.now().isoformat(),
            "description": "Dataset enrichi pour test rapport"
        },
        "parties": [
            {
                "partie_number": 1,
                "mains": [
                    # Début très ordonné (séquence uniforme)
                    {"main_number": 1, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 2, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 3, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 4, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 5, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    
                    # Transition vers plus de diversité
                    {"main_number": 6, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 7, "index5_combined": "0_C_TIE", "index3_result": "TIE"},
                    {"main_number": 8, "index5_combined": "1_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 9, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 10, "index5_combined": "1_C_TIE", "index3_result": "TIE"},
                    
                    # Retour vers plus d'ordre
                    {"main_number": 11, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 12, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 13, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 14, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 15, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    
                    # Fin chaotique
                    {"main_number": 16, "index5_combined": "1_C_TIE", "index3_result": "TIE"},
                    {"main_number": 17, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 18, "index5_combined": "1_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 19, "index5_combined": "0_C_TIE", "index3_result": "TIE"},
                    {"main_number": 20, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"}
                ]
            },
            {
                "partie_number": 2,
                "mains": [
                    # Début chaotique
                    {"main_number": 1, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 2, "index5_combined": "0_C_TIE", "index3_result": "TIE"},
                    {"main_number": 3, "index5_combined": "1_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 4, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 5, "index5_combined": "1_C_TIE", "index3_result": "TIE"},
                    
                    # Stabilisation progressive
                    {"main_number": 6, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 7, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 8, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 9, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 10, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    
                    # Fin ordonnée
                    {"main_number": 11, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 12, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 13, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 14, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 15, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"}
                ]
            }
        ]
    }
    
    filename = "dataset_test_rapport_enrichi.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(dataset_test, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Dataset enrichi créé : {filename}")
    return filename


def test_rapport_enrichi():
    """Test spécifique du rapport enrichi"""
    
    print("🎯 TEST DU RAPPORT ENRICHI")
    print("=" * 40)
    
    # 1. Créer le dataset enrichi
    dataset_path = creer_dataset_test_enrichi()
    
    try:
        # 2. Importer et initialiser l'analyseur
        from analyseur_transitions_index5 import AnalyseurEntropiqueIntegre
        
        print("\n🔥 Initialisation de l'analyseur...")
        analyseur = AnalyseurEntropiqueIntegre(dataset_path)
        
        # 3. Analyse complète
        print("\n📊 Analyse complète...")
        resultats = analyseur.analyser_dataset_complet()
        
        if 'erreur' not in resultats:
            print(f"\n✅ ANALYSE RÉUSSIE !")
            
            # 4. Export du rapport TXT enrichi
            print(f"\n💾 Génération du rapport enrichi...")
            try:
                fichier_txt = analyseur.exporter_resultats_complets(resultats, 'txt')
                
                print(f"✅ Rapport enrichi généré : {fichier_txt}")
                
                # 5. Lire et afficher le début du rapport
                print(f"\n📋 APERÇU DU RAPPORT ENRICHI :")
                print("=" * 50)
                
                with open(fichier_txt, 'r', encoding='utf-8') as f:
                    lignes = f.readlines()
                
                # Afficher les 50 premières lignes
                for i, ligne in enumerate(lignes[:50], 1):
                    print(f"{i:2d}: {ligne.rstrip()}")
                
                if len(lignes) > 50:
                    print(f"\n... et {len(lignes) - 50} lignes supplémentaires")
                
                print(f"\n📊 STATISTIQUES DU RAPPORT :")
                print(f"   Nombre total de lignes : {len(lignes)}")
                print(f"   Taille du fichier : {os.path.getsize(fichier_txt)} octets")
                
                # 6. Rechercher les sections spécifiques
                sections_trouvees = []
                for ligne in lignes:
                    if "ANALYSE DES RATIOS D'ENTROPIE" in ligne:
                        sections_trouvees.append("✅ Analyse des ratios d'entropie")
                    elif "DISTRIBUTION DES RATIOS" in ligne:
                        sections_trouvees.append("✅ Distribution des ratios")
                    elif "PERFORMANCE PAR ZONE DE RATIO" in ligne:
                        sections_trouvees.append("✅ Performance par zone de ratio")
                    elif "EXEMPLES REMARQUABLES" in ligne:
                        sections_trouvees.append("✅ Exemples remarquables")
                    elif "INSIGHTS SUR LES RATIOS" in ligne:
                        sections_trouvees.append("✅ Insights sur les ratios")
                
                print(f"\n🎯 SECTIONS ENRICHIES DÉTECTÉES :")
                for section in sections_trouvees:
                    print(f"   {section}")
                
                return fichier_txt
                
            except Exception as e:
                print(f"❌ Erreur génération rapport : {e}")
                import traceback
                traceback.print_exc()
                return None
        else:
            print(f"❌ Erreur analyse : {resultats['erreur']}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur inattendue : {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Nettoyer le fichier de test
        if os.path.exists(dataset_path):
            os.remove(dataset_path)
            print(f"\n🧹 Dataset de test supprimé : {dataset_path}")


if __name__ == "__main__":
    fichier_rapport = test_rapport_enrichi()
    
    if fichier_rapport:
        print(f"\n🎉 TEST RÉUSSI !")
        print(f"📄 Rapport enrichi disponible : {fichier_rapport}")
        print(f"💡 Ouvrez le fichier pour voir toutes les analyses détaillées des ratios d'entropie")
    else:
        print(f"\n❌ Test échoué")
