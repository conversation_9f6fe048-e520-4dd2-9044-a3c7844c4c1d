================================================================================
TRANSITIONS LES PLUS PROBABLES INDEX5
Pour chaque INDEX5(n), transition vers INDEX5(n+1) la plus probable
Généré le : 2025-06-22 08:43:03
================================================================================

📊 TABLE DES TRANSITIONS PROBABLES
==================================================
0_C_TIE → 1_A_BANKER (17.38%, 15,237 fois)
1_A_TIE → 1_A_BANKER (17.32%, 20,710 fois)
1_B_TIE → 1_A_BANKER (17.30%, 18,790 fois)
1_C_PLAYER → 0_A_BANKER (17.28%, 67,870 fois)
1_B_PLAYER → 1_A_BANKER (17.23%, 87,498 fois)
1_C_TIE → 0_A_PLAYER (17.22%, 15,415 fois)
0_A_PLAYER → 0_A_BANKER (17.21%, 95,632 fois)
1_A_BANKER → 1_A_BANKER (17.20%, 96,903 fois)
0_C_BANKER → 1_A_PLAYER (17.19%, 87,424 fois)
0_B_PLAYER → 0_A_PLAYER (17.18%, 86,382 fois)
0_C_PLAYER → 1_A_PLAYER (17.17%, 66,688 fois)
0_B_BANKER → 0_A_PLAYER (17.17%, 72,462 fois)
0_A_TIE → 0_A_PLAYER (17.16%, 20,229 fois)
1_C_BANKER → 0_A_PLAYER (17.15%, 88,127 fois)
0_B_TIE → 0_A_BANKER (17.15%, 18,611 fois)
1_B_BANKER → 1_A_BANKER (17.13%, 73,144 fois)
0_A_BANKER → 0_A_BANKER (17.13%, 95,229 fois)
1_A_PLAYER → 1_A_PLAYER (17.10%, 96,093 fois)

🔍 ANALYSE DES PATTERNS
==============================

REPETITIONS: 3 cas
  • ('0_A_BANKER', 17.***************)
  • ('1_A_BANKER', 17.**************)
  • ('1_A_PLAYER', 17.***************)

CHANGEMENTS_RESULTAT: 3 cas
  • ('0_A_PLAYER', '0_A_BANKER', 17.***************)
  • ('0_A_TIE', '0_A_PLAYER', 17.**************)
  • ('1_A_TIE', '1_A_BANKER', 17.**************)

TRANSITIONS_COMPLEXES: 10 cas
  • ('0_B_BANKER', '0_A_PLAYER', 17.***************)
  • ('0_C_BANKER', '1_A_PLAYER', 17.**************)
  • ('1_C_BANKER', '0_A_PLAYER', 17.***************)
  • ('1_B_PLAYER', '1_A_BANKER', 17.***************)
  • ('0_C_PLAYER', '1_A_PLAYER', 17.**************)
