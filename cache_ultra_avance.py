#!/usr/bin/env python3
"""
CACHE ULTRA-AVANCÉ POUR ACCÉLÉRATION MAXIMALE
=============================================

Système de cache multi-niveaux pour accélérer drastiquement
le traitement par partie en évitant les recalculs.
"""

import pickle
import hashlib
import numpy as np
from pathlib import Path
import time
from collections import defaultdict


class CacheUltraAvance:
    """
    Cache multi-niveaux pour optimisation maximale du traitement
    """
    
    def __init__(self, cache_dir: str = "cache_ultra_avance"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Caches en mémoire pour accès ultra-rapide
        self.cache_entropies_sequences = {}
        self.cache_entropies_globales = {}
        self.cache_ratios = {}
        self.cache_predictions = {}
        
        # Statistiques de performance
        self.stats = {
            'hits_entropies': 0,
            'miss_entropies': 0,
            'hits_globales': 0,
            'miss_globales': 0,
            'hits_ratios': 0,
            'miss_ratios': 0,
            'temps_economise': 0.0
        }
        
        print("🚀 CACHE ULTRA-AVANCÉ INITIALISÉ")
        print(f"💾 Répertoire : {self.cache_dir}")
    
    def charger_caches_persistants(self):
        """Charge tous les caches depuis le disque"""
        
        print("📁 Chargement des caches persistants...")
        
        # Cache des entropies de séquences
        cache_entropies_file = self.cache_dir / "entropies_sequences.pkl"
        if cache_entropies_file.exists():
            with open(cache_entropies_file, 'rb') as f:
                self.cache_entropies_sequences = pickle.load(f)
            print(f"✅ Cache entropies : {len(self.cache_entropies_sequences):,} séquences")
        
        # Cache des entropies globales
        cache_globales_file = self.cache_dir / "entropies_globales.pkl"
        if cache_globales_file.exists():
            with open(cache_globales_file, 'rb') as f:
                self.cache_entropies_globales = pickle.load(f)
            print(f"✅ Cache globales : {len(self.cache_entropies_globales):,} patterns")
        
        # Cache des ratios
        cache_ratios_file = self.cache_dir / "ratios.pkl"
        if cache_ratios_file.exists():
            with open(cache_ratios_file, 'rb') as f:
                self.cache_ratios = pickle.load(f)
            print(f"✅ Cache ratios : {len(self.cache_ratios):,} combinaisons")
    
    def sauvegarder_caches_persistants(self):
        """Sauvegarde tous les caches sur le disque"""
        
        print("💾 Sauvegarde des caches persistants...")
        
        # Sauvegarder entropies de séquences
        cache_entropies_file = self.cache_dir / "entropies_sequences.pkl"
        with open(cache_entropies_file, 'wb') as f:
            pickle.dump(self.cache_entropies_sequences, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        # Sauvegarder entropies globales
        cache_globales_file = self.cache_dir / "entropies_globales.pkl"
        with open(cache_globales_file, 'wb') as f:
            pickle.dump(self.cache_entropies_globales, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        # Sauvegarder ratios
        cache_ratios_file = self.cache_dir / "ratios.pkl"
        with open(cache_ratios_file, 'wb') as f:
            pickle.dump(self.cache_ratios, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        print("✅ Caches sauvegardés")
    
    def get_entropie_sequence(self, sequence_tuple):
        """Récupère l'entropie d'une séquence depuis le cache ou la calcule"""
        
        if sequence_tuple in self.cache_entropies_sequences:
            self.stats['hits_entropies'] += 1
            return self.cache_entropies_sequences[sequence_tuple]
        
        # Calcul et mise en cache
        start_time = time.time()
        entropie = self._calculer_entropie_shannon(sequence_tuple)
        self.cache_entropies_sequences[sequence_tuple] = entropie
        self.stats['miss_entropies'] += 1
        self.stats['temps_economise'] += time.time() - start_time
        
        return entropie
    
    def get_entropie_globale_progressive(self, sequence_complete, position):
        """Récupère l'entropie globale progressive depuis le cache"""
        
        # Créer une clé de cache basée sur la séquence jusqu'à la position
        sequence_key = tuple(sequence_complete[:position])
        cache_key = (sequence_key, position)
        
        if cache_key in self.cache_entropies_globales:
            self.stats['hits_globales'] += 1
            return self.cache_entropies_globales[cache_key]
        
        # Calcul et mise en cache
        start_time = time.time()
        entropie_globale = self._calculer_entropie_globale_progressive(sequence_complete, position)
        self.cache_entropies_globales[cache_key] = entropie_globale
        self.stats['miss_globales'] += 1
        self.stats['temps_economise'] += time.time() - start_time
        
        return entropie_globale
    
    def get_ratio_entropique(self, entropie_locale, entropie_globale):
        """Récupère le ratio entropique depuis le cache"""
        
        # Arrondir pour créer des clés de cache efficaces
        locale_rounded = round(entropie_locale, 4)
        globale_rounded = round(entropie_globale, 4)
        cache_key = (locale_rounded, globale_rounded)
        
        if cache_key in self.cache_ratios:
            self.stats['hits_ratios'] += 1
            return self.cache_ratios[cache_key]
        
        # Calcul et mise en cache
        if entropie_globale > 0:
            ratio = entropie_locale / entropie_globale
        else:
            ratio = float('inf')
        
        self.cache_ratios[cache_key] = ratio
        self.stats['miss_ratios'] += 1
        
        return ratio
    
    def _calculer_entropie_shannon(self, sequence_tuple):
        """Calcule l'entropie de Shannon d'une séquence"""
        
        # Mapping INDEX5 vers entiers
        index5_to_int = {
            '0_A_BANKER': 0, '0_A_PLAYER': 1, '0_A_TIE': 2,
            '0_B_BANKER': 3, '0_B_PLAYER': 4, '0_B_TIE': 5,
            '0_C_BANKER': 6, '0_C_PLAYER': 7, '0_C_TIE': 8,
            '1_A_BANKER': 9, '1_A_PLAYER': 10, '1_A_TIE': 11,
            '1_B_BANKER': 12, '1_B_PLAYER': 13, '1_B_TIE': 14,
            '1_C_BANKER': 15, '1_C_PLAYER': 16, '1_C_TIE': 17
        }
        
        # Convertir en entiers
        sequence_int = [index5_to_int.get(val, 0) for val in sequence_tuple]
        
        # Compter les occurrences
        counts = np.bincount(sequence_int, minlength=18)
        
        # Calculer les probabilités
        total = len(sequence_int)
        probs = counts / total
        
        # Calculer l'entropie
        entropie = 0.0
        for p in probs:
            if p > 0:
                entropie -= p * np.log2(p)
        
        return entropie
    
    def _calculer_entropie_globale_progressive(self, sequence_complete, position):
        """Calcule l'entropie globale progressive jusqu'à une position"""
        
        # Mapping INDEX5 vers entiers
        index5_to_int = {
            '0_A_BANKER': 0, '0_A_PLAYER': 1, '0_A_TIE': 2,
            '0_B_BANKER': 3, '0_B_PLAYER': 4, '0_B_TIE': 5,
            '0_C_BANKER': 6, '0_C_PLAYER': 7, '0_C_TIE': 8,
            '1_A_BANKER': 9, '1_A_PLAYER': 10, '1_A_TIE': 11,
            '1_B_BANKER': 12, '1_B_PLAYER': 13, '1_B_TIE': 14,
            '1_C_BANKER': 15, '1_C_PLAYER': 16, '1_C_TIE': 17
        }
        
        # Convertir la séquence jusqu'à la position
        sequence_int = [index5_to_int.get(val, 0) for val in sequence_complete[:position]]
        
        # Compter les occurrences
        counts = np.bincount(sequence_int, minlength=18)
        
        # Calculer les probabilités
        total = len(sequence_int)
        if total == 0:
            return 0.0
        
        probs = counts / total
        
        # Calculer l'entropie
        entropie = 0.0
        for p in probs:
            if p > 0:
                entropie -= p * np.log2(p)
        
        return entropie
    
    def precalculer_entropies_sequences_courantes(self, sequences_list):
        """Pré-calcule les entropies des séquences les plus courantes"""
        
        print(f"🔄 Pré-calcul des entropies pour {len(sequences_list):,} séquences...")
        
        start_time = time.time()
        nouvelles_entropies = 0
        
        for sequence in sequences_list:
            if sequence not in self.cache_entropies_sequences:
                entropie = self._calculer_entropie_shannon(sequence)
                self.cache_entropies_sequences[sequence] = entropie
                nouvelles_entropies += 1
        
        end_time = time.time()
        
        print(f"✅ Pré-calcul terminé en {end_time - start_time:.2f}s")
        print(f"📊 {nouvelles_entropies:,} nouvelles entropies calculées")
        print(f"💾 Cache total : {len(self.cache_entropies_sequences):,} séquences")
    
    def afficher_statistiques(self):
        """Affiche les statistiques de performance du cache"""
        
        print(f"\n📊 STATISTIQUES DE PERFORMANCE DU CACHE")
        print("=" * 50)
        
        # Entropies de séquences
        total_entropies = self.stats['hits_entropies'] + self.stats['miss_entropies']
        if total_entropies > 0:
            taux_hit_entropies = (self.stats['hits_entropies'] / total_entropies) * 100
            print(f"🎯 Entropies séquences :")
            print(f"   Hits : {self.stats['hits_entropies']:,} ({taux_hit_entropies:.1f}%)")
            print(f"   Miss : {self.stats['miss_entropies']:,}")
        
        # Entropies globales
        total_globales = self.stats['hits_globales'] + self.stats['miss_globales']
        if total_globales > 0:
            taux_hit_globales = (self.stats['hits_globales'] / total_globales) * 100
            print(f"🌍 Entropies globales :")
            print(f"   Hits : {self.stats['hits_globales']:,} ({taux_hit_globales:.1f}%)")
            print(f"   Miss : {self.stats['miss_globales']:,}")
        
        # Ratios
        total_ratios = self.stats['hits_ratios'] + self.stats['miss_ratios']
        if total_ratios > 0:
            taux_hit_ratios = (self.stats['hits_ratios'] / total_ratios) * 100
            print(f"📊 Ratios entropiques :")
            print(f"   Hits : {self.stats['hits_ratios']:,} ({taux_hit_ratios:.1f}%)")
            print(f"   Miss : {self.stats['miss_ratios']:,}")
        
        # Temps économisé
        if self.stats['temps_economise'] > 0:
            print(f"⏱️ Temps économisé : {self.stats['temps_economise']:.3f}s")
        
        # Taille des caches
        print(f"\n💾 TAILLE DES CACHES :")
        print(f"   Entropies séquences : {len(self.cache_entropies_sequences):,}")
        print(f"   Entropies globales : {len(self.cache_entropies_globales):,}")
        print(f"   Ratios : {len(self.cache_ratios):,}")


def tester_cache_ultra_avance():
    """Test du système de cache ultra-avancé"""
    
    print("🧪 TEST DU CACHE ULTRA-AVANCÉ")
    print("=" * 40)
    
    # Initialiser le cache
    cache = CacheUltraAvance()
    cache.charger_caches_persistants()
    
    # Séquences de test
    sequences_test = [
        ('1_A_BANKER', '0_B_PLAYER', '1_C_TIE', '0_A_BANKER'),
        ('0_C_BANKER', '1_A_PLAYER', '0_B_TIE', '1_C_BANKER'),
        ('1_B_PLAYER', '0_A_BANKER', '1_C_PLAYER', '0_B_TIE'),
    ]
    
    print(f"\n🔄 Test avec {len(sequences_test)} séquences...")
    
    # Premier passage (miss)
    start_time = time.time()
    for sequence in sequences_test:
        entropie = cache.get_entropie_sequence(sequence)
        print(f"   {sequence[0]}...{sequence[-1]} → {entropie:.4f}")
    temps_premier = time.time() - start_time
    
    # Deuxième passage (hits)
    start_time = time.time()
    for sequence in sequences_test:
        entropie = cache.get_entropie_sequence(sequence)
    temps_second = time.time() - start_time
    
    # Calculer l'accélération
    if temps_second > 0:
        acceleration = temps_premier / temps_second
        print(f"\n🚀 Accélération cache : {acceleration:.1f}x")
    
    # Afficher les statistiques
    cache.afficher_statistiques()
    
    # Sauvegarder les caches
    cache.sauvegarder_caches_persistants()
    
    return cache


if __name__ == "__main__":
    tester_cache_ultra_avance()
