#!/usr/bin/env python3
"""
TEST ISOLÉ DE L'ANALYSEUR ENTROPIQUE INTÉGRÉ
============================================

Test spécifique pour valider le fonctionnement de la nouvelle classe
AnalyseurEntropiqueIntegre sans dépendre du dataset principal.
"""

import json
import os
from datetime import datetime


def creer_dataset_test():
    """Crée un petit dataset de test pour valider le système"""
    
    dataset_test = {
        "metadata": {
            "version": "1.0",
            "created": datetime.now().isoformat(),
            "description": "Dataset de test pour analyseur entropique"
        },
        "parties": [
            {
                "partie_number": 1,
                "mains": [
                    {"main_number": 1, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 2, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 3, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 4, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 5, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 6, "index5_combined": "0_C_TIE", "index3_result": "TIE"},
                    {"main_number": 7, "index5_combined": "1_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 8, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 9, "index5_combined": "1_C_TIE", "index3_result": "TIE"},
                    {"main_number": 10, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 11, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 12, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"}
                ]
            },
            {
                "partie_number": 2,
                "mains": [
                    {"main_number": 1, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 2, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 3, "index5_combined": "1_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 4, "index5_combined": "1_C_TIE", "index3_result": "TIE"},
                    {"main_number": 5, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 6, "index5_combined": "0_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 7, "index5_combined": "0_C_TIE", "index3_result": "TIE"},
                    {"main_number": 8, "index5_combined": "1_A_BANKER", "index3_result": "BANKER"},
                    {"main_number": 9, "index5_combined": "1_B_PLAYER", "index3_result": "PLAYER"},
                    {"main_number": 10, "index5_combined": "0_A_BANKER", "index3_result": "BANKER"}
                ]
            }
        ]
    }
    
    filename = "dataset_test_entropique.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(dataset_test, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Dataset de test créé : {filename}")
    return filename


def test_analyseur_entropique_complet():
    """Test complet de l'analyseur entropique intégré"""
    
    print("🎯 TEST COMPLET ANALYSEUR ENTROPIQUE INTÉGRÉ")
    print("=" * 60)
    
    # 1. Créer le dataset de test
    dataset_path = creer_dataset_test()
    
    try:
        # 2. Importer la classe (doit être dans le même répertoire)
        from analyseur_transitions_index5 import AnalyseurEntropiqueIntegre
        
        print("\n🔥 Initialisation de l'analyseur...")
        analyseur = AnalyseurEntropiqueIntegre(dataset_path)
        
        # 3. Test d'analyse complète
        print("\n📊 Lancement de l'analyse complète...")
        resultats = analyseur.analyser_dataset_complet()
        
        if 'erreur' not in resultats:
            print(f"\n✅ ANALYSE RÉUSSIE !")
            
            # Afficher les statistiques
            stats = resultats['statistiques_globales']
            print(f"\n📈 RÉSULTATS :")
            print(f"   Parties analysées : {resultats['nb_parties_analysees']}")
            print(f"   Parties réussies : {resultats['parties_reussies']}")
            print(f"   Total prédictions : {stats['total_predictions_globales']:,}")
            print(f"   Taux de succès global : {stats['taux_succes_global']:.2f}%")
            
            # Détail par type de prédiction
            if stats['performance_par_type']:
                print(f"\n🎯 PERFORMANCE PAR TYPE :")
                for pred_type, perf in stats['performance_par_type'].items():
                    print(f"   {pred_type:25s} : {perf['taux_succes']:6.2f}% ({perf['total_predictions']:,} prédictions)")
            
            # Export des résultats
            print(f"\n💾 Export des résultats...")
            try:
                fichier_json = analyseur.exporter_resultats_complets(resultats, 'json')
                fichier_txt = analyseur.exporter_resultats_complets(resultats, 'txt')
                
                print(f"✅ Fichiers exportés :")
                print(f"   JSON : {fichier_json}")
                print(f"   TXT  : {fichier_txt}")
            except Exception as e:
                print(f"⚠️ Erreur export : {e}")
            
            # Analyse détaillée d'une partie
            if resultats['resultats_parties']:
                print(f"\n🔍 ANALYSE DÉTAILLÉE (première partie) :")
                premiere_partie = list(resultats['resultats_parties'].values())[0]
                
                print(f"   Partie ID : {premiere_partie['partie_id']}")
                print(f"   Nombre de mains : {premiere_partie['nb_mains']}")
                print(f"   Séquences locales : {len(premiere_partie['sequences_locales'])}")
                print(f"   Ratios calculés : {len(premiere_partie['ratios_desordre'])}")
                print(f"   Prédictions : {len(premiere_partie['predictions'])}")
                print(f"   Validations : {len(premiere_partie['validations'])}")
                
                # Quelques exemples de prédictions
                if premiere_partie['predictions']:
                    print(f"\n   📋 Exemples de prédictions :")
                    for i, (seq_id, pred) in enumerate(list(premiere_partie['predictions'].items())[:3]):
                        print(f"      {i+1}. Position {pred['position']} : {pred['prediction_tendance']} (confiance: {pred['prediction_confiance']:.1f}%)")
            
            return resultats
        else:
            print(f"❌ Erreur dans l'analyse : {resultats['erreur']}")
            return None
            
    except ImportError as e:
        print(f"❌ Erreur import : {e}")
        print("   Assurez-vous que analyseur_transitions_index5.py est dans le même répertoire")
        return None
    except Exception as e:
        print(f"❌ Erreur inattendue : {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Nettoyer le fichier de test
        if os.path.exists(dataset_path):
            os.remove(dataset_path)
            print(f"\n🧹 Dataset de test supprimé : {dataset_path}")


def test_modules_individuels():
    """Test des modules individuels avant intégration"""
    
    print("\n🔧 TEST DES MODULES INDIVIDUELS")
    print("-" * 40)
    
    try:
        # Test module signatures
        print("1. Test module signatures entropiques...")
        from module_signatures_entropiques import GenerateurSignaturesEntropiques
        
        gen = GenerateurSignaturesEntropiques()
        test_sequence = ('0_A_BANKER', '0_A_BANKER', '0_B_PLAYER', '0_C_TIE')
        signature = gen.calculer_signature_entropique(test_sequence)
        
        print(f"   ✅ Signature calculée : entropie = {signature['entropie_shannon']:.4f}")
        
        # Test module entropie globale
        print("2. Test module entropie globale progressive...")
        from module_entropie_globale_progressive import CalculateurEntropieGlobaleProgressive
        
        calc = CalculateurEntropieGlobaleProgressive()
        
        partie_test = {
            'partie_number': 'TEST',
            'mains': [
                {'index5_combined': '0_A_BANKER'},
                {'index5_combined': '0_B_PLAYER'},
                {'index5_combined': '0_C_TIE'},
                {'index5_combined': '1_A_BANKER'}
            ]
        }
        
        resultat = calc.calculer_entropies_globales_partie(partie_test)
        
        if 'erreur' not in resultat:
            entropie_finale = resultat['analyse_convergence']['entropie_finale']
            print(f"   ✅ Entropie globale calculée : {entropie_finale:.4f}")
        else:
            print(f"   ❌ Erreur : {resultat['erreur']}")
        
        print("✅ Modules individuels fonctionnels")
        return True
        
    except Exception as e:
        print(f"❌ Erreur modules individuels : {e}")
        return False


if __name__ == "__main__":
    # Test des modules individuels d'abord
    if test_modules_individuels():
        # Puis test complet
        resultats = test_analyseur_entropique_complet()
        
        if resultats:
            print(f"\n🎉 TOUS LES TESTS RÉUSSIS !")
            print("🎯 L'analyseur entropique intégré est opérationnel")
        else:
            print(f"\n❌ Échec des tests")
    else:
        print(f"\n❌ Modules individuels défaillants - arrêt des tests")
