#!/usr/bin/env python3
"""
TEST COMPLET DU RATIO DE DÉSORDRE
=================================

Script de test pour calculer les ratios de désordre en combinant :
1. Les signatures entropiques locales (séquences 4-5)
2. Les entropies globales progressives (main 1 → main n)

OBJECTIF : Démontrer le calcul des ratios de désordre et la prédiction
des tendances entropiques pour chaque main d'une partie.

Usage:
    python test_ratio_desordre_complet.py
"""

from module_signatures_entropiques import GenerateurSignaturesEntropiques
from module_entropie_globale_progressive import CalculateurEntropieGlobaleProgressive
import json
import time


def test_ratio_desordre_partie_fictive():
    """Test complet avec une partie fictive"""
    
    print("🎯 TEST COMPLET DU RATIO DE DÉSORDRE")
    print("=" * 50)
    
    # Créer les modules
    print("1. Initialisation des modules...")
    generateur_signatures = GenerateurSignaturesEntropiques()
    calculateur_global = CalculateurEntropieGlobaleProgressive()
    
    # Partie de test avec patterns variés
    partie_test = {
        'partie_number': 'RATIO_TEST_001',
        'mains': [
            # Début très ordonné (séquence uniforme)
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_A_BANKER'},
            
            # Transition vers plus de diversité
            {'index5_combined': '0_B_PLAYER'},
            {'index5_combined': '0_C_TIE'},
            {'index5_combined': '1_A_BANKER'},
            {'index5_combined': '1_B_PLAYER'},
            {'index5_combined': '1_C_TIE'},
            
            # Retour vers plus d'ordre
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_B_PLAYER'},
            {'index5_combined': '0_B_PLAYER'},
            {'index5_combined': '0_A_BANKER'},
            
            # Fin chaotique
            {'index5_combined': '1_C_TIE'},
            {'index5_combined': '0_B_PLAYER'},
            {'index5_combined': '1_A_BANKER'},
            {'index5_combined': '0_C_TIE'},
            {'index5_combined': '1_B_PLAYER'}
        ]
    }
    
    print(f"✅ Partie de test créée : {len(partie_test['mains'])} mains")
    
    # 2. Calculer les entropies globales progressives
    print("\n2. Calcul des entropies globales progressives...")
    resultats_globaux = calculateur_global.calculer_entropies_globales_partie(partie_test)
    
    if 'erreur' in resultats_globaux:
        print(f"❌ Erreur : {resultats_globaux['erreur']}")
        return
    
    print(f"✅ Entropies globales calculées")
    print(f"   Entropie finale : {resultats_globaux['analyse_convergence']['entropie_finale']:.4f}")
    
    # 3. Calculer les signatures locales pour les séquences de longueur 5
    print("\n3. Calcul des signatures locales (longueur 5)...")
    
    sequence_complete = [main['index5_combined'] for main in partie_test['mains']]
    signatures_locales = {}
    
    # Calculer pour chaque position où on peut avoir une séquence de longueur 5
    for position in range(4, len(sequence_complete)):  # Position 4 = 5ème main (index 0-based)
        debut_sequence = position - 4
        sequence_locale = tuple(sequence_complete[debut_sequence:position + 1])
        
        # Calculer la signature de cette séquence locale
        signature = generateur_signatures.calculer_signature_entropique(sequence_locale)
        signatures_locales[position + 1] = signature  # Position 1-based pour cohérence
    
    print(f"✅ {len(signatures_locales)} signatures locales calculées")
    
    # 4. Calculer les ratios de désordre
    print("\n4. Calcul des ratios de désordre...")
    
    ratios_desordre = {}
    entropies_globales = resultats_globaux['entropies_globales']
    
    for position, signature_locale in signatures_locales.items():
        if position in entropies_globales:
            
            entropie_locale = signature_locale['entropie_shannon']
            entropie_globale = entropies_globales[position]['entropie_globale']
            
            # Calculer le ratio
            if entropie_globale > 0:
                ratio = entropie_locale / entropie_globale
            else:
                ratio = float('inf')
            
            # Prédire la tendance
            prediction = predire_tendance_desordre(ratio)
            
            ratios_desordre[position] = {
                'entropie_locale': entropie_locale,
                'entropie_globale': entropie_globale,
                'ratio_desordre': ratio,
                'prediction': prediction,
                'sequence_locale': signature_locale['sequence']
            }
    
    print(f"✅ {len(ratios_desordre)} ratios de désordre calculés")
    
    # 5. Afficher les résultats détaillés
    print("\n5. ANALYSE DÉTAILLÉE DES RATIOS DE DÉSORDRE")
    print("=" * 60)
    
    for position in sorted(ratios_desordre.keys()):
        data = ratios_desordre[position]
        
        print(f"\n📍 MAIN {position}")
        print(f"   Séquence locale : {data['sequence_locale']}")
        print(f"   Entropie locale : {data['entropie_locale']:.4f}")
        print(f"   Entropie globale: {data['entropie_globale']:.4f}")
        print(f"   Ratio désordre  : {data['ratio_desordre']:.4f}")
        print(f"   🎯 PRÉDICTION   : {data['prediction']['tendance']}")
        print(f"   Intensité       : {data['prediction']['intensité']}")
        print(f"   Confiance       : {data['prediction']['confiance']:.1f}%")
        print(f"   Explication     : {data['prediction']['explication']}")
    
    # 6. Synthèse des prédictions
    print("\n6. SYNTHÈSE DES PRÉDICTIONS")
    print("-" * 40)
    
    predictions_count = {}
    for data in ratios_desordre.values():
        tendance = data['prediction']['tendance']
        predictions_count[tendance] = predictions_count.get(tendance, 0) + 1
    
    for tendance, count in predictions_count.items():
        pourcentage = (count / len(ratios_desordre)) * 100
        print(f"   {tendance:25s} : {count:2d} fois ({pourcentage:5.1f}%)")
    
    # 7. Export des résultats
    print("\n7. Export des résultats...")
    
    resultats_complets = {
        'partie_test': partie_test,
        'entropies_globales': resultats_globaux,
        'signatures_locales': {str(k): v for k, v in signatures_locales.items()},
        'ratios_desordre': {str(k): v for k, v in ratios_desordre.items()},
        'synthese_predictions': predictions_count
    }
    
    # Sauvegarder
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"test_ratio_desordre_complet_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(resultats_complets, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ Résultats exportés : {filename}")
    
    return ratios_desordre


def predire_tendance_desordre(ratio_desordre, seuils=(0.7, 1.3)):
    """
    Prédit la tendance du désordre basée sur le ratio
    
    Args:
        ratio_desordre: Ratio entropie locale / entropie globale
        seuils: Seuils bas et haut pour la classification
        
    Returns:
        Dict: Prédiction de tendance avec confiance
    """
    
    seuil_bas, seuil_haut = seuils
    
    if ratio_desordre < seuil_bas:
        return {
            'tendance': 'VERS_PLUS_DE_DÉSORDRE',
            'intensité': 'FORTE' if ratio_desordre < 0.5 else 'MODÉRÉE',
            'confiance': min(95, (seuil_bas - ratio_desordre) * 100),
            'explication': 'Ordre local excessif → Correction vers chaos'
        }
    
    elif ratio_desordre > seuil_haut:
        return {
            'tendance': 'VERS_MOINS_DE_DÉSORDRE',
            'intensité': 'FORTE' if ratio_desordre > 1.5 else 'MODÉRÉE',
            'confiance': min(95, (ratio_desordre - seuil_haut) * 100),
            'explication': 'Chaos local excessif → Correction vers ordre'
        }
    
    else:
        return {
            'tendance': 'MAINTIEN_ÉQUILIBRE',
            'intensité': 'STABLE',
            'confiance': 70,
            'explication': 'Cohérence locale/globale → Continuation'
        }


def analyser_evolution_ratios(ratios_desordre):
    """Analyse l'évolution des ratios au cours de la partie"""
    
    print("\n📈 ÉVOLUTION DES RATIOS DE DÉSORDRE")
    print("-" * 45)
    
    positions = sorted(ratios_desordre.keys())
    ratios = [ratios_desordre[pos]['ratio_desordre'] for pos in positions]
    
    # Tendance générale
    if len(ratios) >= 3:
        import numpy as np
        tendance = np.polyfit(range(len(ratios)), ratios, 1)[0]
        
        if tendance > 0.01:
            evolution = "CROISSANTE (vers plus de chaos local)"
        elif tendance < -0.01:
            evolution = "DÉCROISSANTE (vers plus d'ordre local)"
        else:
            evolution = "STABLE"
        
        print(f"Tendance générale : {evolution}")
        print(f"Pente : {tendance:.6f}")
    
    # Points extrêmes
    ratio_min = min(ratios)
    ratio_max = max(ratios)
    pos_min = positions[ratios.index(ratio_min)]
    pos_max = positions[ratios.index(ratio_max)]
    
    print(f"Ratio minimum : {ratio_min:.4f} (Main {pos_min})")
    print(f"Ratio maximum : {ratio_max:.4f} (Main {pos_max})")
    print(f"Amplitude : {ratio_max - ratio_min:.4f}")


if __name__ == "__main__":
    # Test principal
    ratios = test_ratio_desordre_partie_fictive()
    
    if ratios:
        # Analyse complémentaire
        analyser_evolution_ratios(ratios)
    
    print(f"\n🎉 TEST COMPLET TERMINÉ AVEC SUCCÈS !")
    print("🎯 Vous disposez maintenant d'un système complet de calcul")
    print("   des ratios de désordre et de prédiction des tendances entropiques !")
