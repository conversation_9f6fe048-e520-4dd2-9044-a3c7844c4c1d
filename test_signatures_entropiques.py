#!/usr/bin/env python3
"""
TEST DU MODULE SIGNATURES ENTROPIQUES
=====================================

Script de test pour générer et analyser toutes les signatures entropiques
des séquences BCT de longueur 4 et 5.

Usage:
    python test_signatures_entropiques.py
"""

from module_signatures_entropiques import GenerateurSignaturesEntropiques
import time


def test_generation_signatures():
    """Test complet de la génération des signatures entropiques"""
    
    print("🎯 TEST COMPLET DU MODULE SIGNATURES ENTROPIQUES")
    print("=" * 60)
    
    # Créer le générateur
    print("1. Initialisation du générateur...")
    generateur = GenerateurSignaturesEntropiques()
    
    # Test de génération longueur 4
    print("\n2. Génération des signatures longueur 4...")
    start_time = time.time()
    signatures_4 = generateur.generer_toutes_sequences_longueur_4()
    time_4 = time.time() - start_time
    
    print(f"   ✅ {len(signatures_4):,} signatures longueur 4 générées en {time_4:.2f}s")
    
    # Test de génération longueur 5
    print("\n3. Génération des signatures longueur 5...")
    start_time = time.time()
    signatures_5 = generateur.generer_toutes_sequences_longueur_5()
    time_5 = time.time() - start_time
    
    print(f"   ✅ {len(signatures_5):,} signatures longueur 5 générées en {time_5:.2f}s")
    
    # Analyse du catalogue
    print("\n4. Analyse du catalogue...")
    start_time = time.time()
    rapport = generateur.analyser_catalogue_signatures()
    time_analyse = time.time() - start_time
    
    print(f"   ✅ Analyse complétée en {time_analyse:.2f}s")
    
    # Affichage des statistiques
    print("\n📊 STATISTIQUES GÉNÉRALES :")
    print("-" * 40)
    print(f"Séquences longueur 4 : {rapport['longueur_4']['nb_sequences_total']:,}")
    print(f"Séquences longueur 5 : {rapport['longueur_5']['nb_sequences_total']:,}")
    print(f"Ratio 5/4 : {rapport['comparaison_longueurs']['ratio_sequences']:.2f}")
    
    print(f"\nEntropie moyenne longueur 4 : {rapport['comparaison_longueurs']['entropie_moyenne_4']:.4f}")
    print(f"Entropie moyenne longueur 5 : {rapport['comparaison_longueurs']['entropie_moyenne_5']:.4f}")
    
    # Répartition par classe prédictive
    print(f"\n📈 RÉPARTITION PAR CLASSE PRÉDICTIVE (longueur 4) :")
    for classe, nb in rapport['longueur_4']['repartition_par_classe'].items():
        pourcentage = (nb / rapport['longueur_4']['nb_sequences_total']) * 100
        print(f"   {classe:20s} : {nb:6,} ({pourcentage:5.1f}%)")
    
    print(f"\n📈 RÉPARTITION PAR CLASSE PRÉDICTIVE (longueur 5) :")
    for classe, nb in rapport['longueur_5']['repartition_par_classe'].items():
        pourcentage = (nb / rapport['longueur_5']['nb_sequences_total']) * 100
        print(f"   {classe:20s} : {nb:6,} ({pourcentage:5.1f}%)")
    
    # Top séquences
    print(f"\n🏆 TOP 10 SÉQUENCES LONGUEUR 4 (par potentiel prédictif) :")
    top_4 = rapport['recommandations_predictives']['top_100_longueur_4'][:10]
    for i, seq_info in enumerate(top_4, 1):
        print(f"   {i:2d}. Entropie: {seq_info['entropie']:.4f} | Potentiel: {seq_info['potentiel']:5.1f}% | {seq_info['classe']}")
        print(f"       Séquence: {seq_info['sequence']}")
    
    print(f"\n🏆 TOP 10 SÉQUENCES LONGUEUR 5 (par potentiel prédictif) :")
    top_5 = rapport['recommandations_predictives']['top_100_longueur_5'][:10]
    for i, seq_info in enumerate(top_5, 1):
        print(f"   {i:2d}. Entropie: {seq_info['entropie']:.4f} | Potentiel: {seq_info['potentiel']:5.1f}% | {seq_info['classe']}")
        print(f"       Séquence: {seq_info['sequence']}")
    
    # Export des résultats
    print("\n5. Export des résultats...")
    fichier_json = generateur.exporter_catalogue_complet('json')
    fichier_txt = generateur.exporter_catalogue_complet('txt')
    
    print(f"   ✅ Catalogue JSON : {fichier_json}")
    print(f"   ✅ Rapport TXT : {fichier_txt}")
    
    # Test de recherche
    print("\n6. Test de recherche de signature...")
    sequence_test = ('0_A_BANKER', '0_A_BANKER', '0_A_BANKER', '0_A_BANKER')
    signature_trouvee = generateur.rechercher_signature(sequence_test)
    
    if signature_trouvee:
        print(f"   ✅ Signature trouvée pour {sequence_test}")
        print(f"      Entropie: {signature_trouvee['entropie_shannon']:.4f}")
        print(f"      Potentiel: {signature_trouvee['potentiel_predictif']:.1f}%")
        print(f"      Classe: {signature_trouvee['classe_predictive']}")
    else:
        print(f"   ❌ Signature non trouvée pour {sequence_test}")
    
    # Test de filtrage
    print("\n7. Test de filtrage par potentiel...")
    sequences_filtrees = generateur.filtrer_par_potentiel(potentiel_min=80.0)
    print(f"   ✅ {len(sequences_filtrees):,} séquences avec potentiel ≥ 80%")
    
    sequences_filtrees_90 = generateur.filtrer_par_potentiel(potentiel_min=90.0)
    print(f"   ✅ {len(sequences_filtrees_90):,} séquences avec potentiel ≥ 90%")
    
    print(f"\n🎯 TEST COMPLET TERMINÉ AVEC SUCCÈS !")
    print(f"Temps total : {time_4 + time_5 + time_analyse:.2f}s")
    
    return generateur, rapport


def test_sequences_specifiques():
    """Test avec des séquences spécifiques intéressantes"""
    
    print("\n🔬 TEST DE SÉQUENCES SPÉCIFIQUES")
    print("=" * 40)
    
    generateur = GenerateurSignaturesEntropiques()
    
    # Séquences de test
    sequences_test = [
        # Séquence uniforme (entropie 0)
        ('0_A_BANKER', '0_A_BANKER', '0_A_BANKER', '0_A_BANKER'),
        
        # Séquence avec alternance INDEX1 (règle C)
        ('0_C_BANKER', '1_A_PLAYER', '1_C_TIE', '0_B_BANKER'),
        
        # Séquence très diversifiée
        ('0_A_BANKER', '0_B_PLAYER', '0_C_TIE', '1_A_BANKER'),
        
        # Séquence longueur 5 uniforme
        ('1_B_PLAYER', '1_B_PLAYER', '1_B_PLAYER', '1_B_PLAYER', '1_B_PLAYER'),
        
        # Séquence longueur 5 diversifiée
        ('0_A_BANKER', '0_B_PLAYER', '0_C_TIE', '1_A_BANKER', '1_B_PLAYER')
    ]
    
    for i, sequence in enumerate(sequences_test, 1):
        print(f"\n{i}. Test séquence : {sequence}")
        
        # Vérifier si elle respecte les règles BCT
        if len(sequence) > 1:
            valide = True
            for j in range(len(sequence) - 1):
                transitions_valides = generateur.generer_transitions_valides(sequence[j])
                if sequence[j + 1] not in transitions_valides:
                    valide = False
                    break
            
            print(f"   Respecte les règles BCT : {'✅ OUI' if valide else '❌ NON'}")
            
            if valide:
                signature = generateur.calculer_signature_entropique(sequence)
                print(f"   Entropie Shannon : {signature['entropie_shannon']:.4f}")
                print(f"   Distribution : {signature['distribution_pattern']}")
                print(f"   Potentiel prédictif : {signature['potentiel_predictif']:.1f}%")
                print(f"   Classe : {signature['classe_predictive']}")


if __name__ == "__main__":
    # Test principal
    generateur, rapport = test_generation_signatures()
    
    # Test de séquences spécifiques
    test_sequences_specifiques()
    
    print(f"\n🎉 TOUS LES TESTS TERMINÉS AVEC SUCCÈS !")
