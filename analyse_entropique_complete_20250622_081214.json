{
  "dataset_path": "dataset_test_entropique.json",
  "nb_parties_analysees": 2,
  "parties_reussies": 2,
  "parties_echouees": 0,
  "resultats_parties": {
    "1": {
      "partie_id": 1,
      "nb_mains": 12,
      "entropies_globales": {
        "partie_id": 1,
        "nb_mains_total": 12,
        "entropies_globales": {
          "1": {
            "entropie_globale": -0.0,
            "longueur_sequence": 1,
            "nb_valeurs_distinctes": 1,
            "distribution_pattern": [
              1
            ],
            "entropie_max_theorique": 0.0,
            "pourcentage_entropie_max": 0,
            "sequence_globale": [
              "0_A_BANKER"
            ]
          },
          "2": {
            "entropie_globale": -0.0,
            "longueur_sequence": 2,
            "nb_valeurs_distinctes": 1,
            "distribution_pattern": [
              2
            ],
            "entropie_max_theorique": 1.0,
            "pourcentage_entropie_max": -0.0,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER"
            ]
          },
          "3": {
            "entropie_globale": -0.0,
            "longueur_sequence": 3,
            "nb_valeurs_distinctes": 1,
            "distribution_pattern": [
              3
            ],
            "entropie_max_theorique": 1.585,
            "pourcentage_entropie_max": -0.0,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER"
            ]
          },
          "4": {
            "entropie_globale": -0.0,
            "longueur_sequence": 4,
            "nb_valeurs_distinctes": 1,
            "distribution_pattern": [
              4
            ],
            "entropie_max_theorique": 2.0,
            "pourcentage_entropie_max": -0.0,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER"
            ]
          },
          "5": {
            "entropie_globale": 0.7219,
            "longueur_sequence": 5,
            "nb_valeurs_distinctes": 2,
            "distribution_pattern": [
              4,
              1
            ],
            "entropie_max_theorique": 2.3219,
            "pourcentage_entropie_max": 31.1,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER"
            ]
          },
          "6": {
            "entropie_globale": 1.2516,
            "longueur_sequence": 6,
            "nb_valeurs_distinctes": 3,
            "distribution_pattern": [
              4,
              1,
              1
            ],
            "entropie_max_theorique": 2.585,
            "pourcentage_entropie_max": 48.4,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE"
            ]
          },
          "7": {
            "entropie_globale": 1.6645,
            "longueur_sequence": 7,
            "nb_valeurs_distinctes": 4,
            "distribution_pattern": [
              4,
              1,
              1,
              1
            ],
            "entropie_max_theorique": 2.8074,
            "pourcentage_entropie_max": 59.3,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE",
              "1_A_BANKER"
            ]
          },
          "8": {
            "entropie_globale": 2.0,
            "longueur_sequence": 8,
            "nb_valeurs_distinctes": 5,
            "distribution_pattern": [
              4,
              1,
              1,
              1,
              1
            ],
            "entropie_max_theorique": 3.0,
            "pourcentage_entropie_max": 66.7,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE",
              "1_A_BANKER",
              "1_B_PLAYER"
            ]
          },
          "9": {
            "entropie_globale": 2.281,
            "longueur_sequence": 9,
            "nb_valeurs_distinctes": 6,
            "distribution_pattern": [
              4,
              1,
              1,
              1,
              1,
              1
            ],
            "entropie_max_theorique": 3.1699,
            "pourcentage_entropie_max": 72.0,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE",
              "1_A_BANKER",
              "1_B_PLAYER",
              "1_C_TIE"
            ]
          },
          "10": {
            "entropie_globale": 2.161,
            "longueur_sequence": 10,
            "nb_valeurs_distinctes": 6,
            "distribution_pattern": [
              5,
              1,
              1,
              1,
              1,
              1
            ],
            "entropie_max_theorique": 3.3219,
            "pourcentage_entropie_max": 65.1,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE",
              "1_A_BANKER",
              "1_B_PLAYER",
              "1_C_TIE",
              "0_A_BANKER"
            ]
          },
          "11": {
            "entropie_globale": 2.2222,
            "longueur_sequence": 11,
            "nb_valeurs_distinctes": 6,
            "distribution_pattern": [
              5,
              2,
              1,
              1,
              1,
              1
            ],
            "entropie_max_theorique": 3.4594,
            "pourcentage_entropie_max": 64.2,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE",
              "1_A_BANKER",
              "1_B_PLAYER",
              "1_C_TIE",
              "0_A_BANKER",
              "0_B_PLAYER"
            ]
          },
          "12": {
            "entropie_globale": 2.1258,
            "longueur_sequence": 12,
            "nb_valeurs_distinctes": 6,
            "distribution_pattern": [
              6,
              2,
              1,
              1,
              1,
              1
            ],
            "entropie_max_theorique": 3.585,
            "pourcentage_entropie_max": 59.3,
            "sequence_globale": [
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_C_TIE",
              "1_A_BANKER",
              "1_B_PLAYER",
              "1_C_TIE",
              "0_A_BANKER",
              "0_B_PLAYER",
              "0_A_BANKER"
            ]
          }
        },
        "analyse_convergence": {
          "entropie_finale": 2.1258,
          "entropie_theorique_limite": 4.1699,
          "convergence_vers_limite": 