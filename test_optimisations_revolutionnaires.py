#!/usr/bin/env python3
"""
TEST DES OPTIMISATIONS RÉVOLUTIONNAIRES
=======================================

Test pour vérifier les gains de performance avec :
1. Numba JIT compilation
2. Vectorisation NumPy massive
3. Memory mapping 24GB
4. Multiprocessing 8 cœurs

Objectif : Passer de 102.77s pour 3 parties à quelques secondes !
"""

import time
import os
import numpy as np
from datetime import datetime


def test_optimisations_disponibles():
    """Test de disponibilité des optimisations"""
    
    print("🔍 VÉRIFICATION DES OPTIMISATIONS DISPONIBLES")
    print("=" * 50)
    
    optimisations = {}
    
    # Test Numba
    try:
        from numba import jit, prange
        optimisations['numba'] = True
        print("✅ Numba JIT : Disponible")
    except ImportError:
        optimisations['numba'] = False
        print("❌ Numba JIT : Non disponible")
    
    # Test orjson
    try:
        import orjson
        optimisations['orjson'] = True
        print("✅ orjson : Disponible")
    except ImportError:
        optimisations['orjson'] = False
        print("❌ orjson : Non disponible")
    
    # Test multiprocessing
    import multiprocessing
    nb_cores = multiprocessing.cpu_count()
    optimisations['multiprocessing'] = nb_cores
    print(f"✅ Multiprocessing : {nb_cores} cœurs disponibles")
    
    # Test mémoire
    import psutil
    ram_gb = psutil.virtual_memory().total / (1024**3)
    optimisations['ram_gb'] = ram_gb
    print(f"✅ RAM : {ram_gb:.1f} GB disponible")
    
    return optimisations


def test_performance_entropie_jit():
    """Test de performance des calculs d'entropie avec JIT"""
    
    print(f"\n⚡ TEST PERFORMANCE ENTROPIE JIT")
    print("=" * 40)
    
    # Générer des données de test
    nb_sequences = 10000
    longueur_sequence = 5
    
    # Données aléatoires (18 valeurs INDEX5)
    sequences_test = np.random.randint(0, 18, size=(nb_sequences, longueur_sequence))
    
    print(f"📊 Test avec {nb_sequences:,} séquences de longueur {longueur_sequence}")
    
    # Test version standard
    print("\n1️⃣ Version standard (NumPy)...")
    start_time = time.time()
    
    entropies_standard = []
    for sequence in sequences_test:
        counts = np.bincount(sequence, minlength=18)
        probs = counts / len(sequence)
        entropie = -np.sum(probs * np.log2(probs + 1e-10))
        entropies_standard.append(entropie)
    
    temps_standard = time.time() - start_time
    print(f"   Temps : {temps_standard:.3f}s")
    
    # Test version JIT (si disponible)
    try:
        from analyseur_transitions_index5 import calcul_entropies_batch_jit, HAS_NUMBA
        
        if HAS_NUMBA:
            print("\n2️⃣ Version JIT (Numba)...")
            start_time = time.time()
            
            entropies_jit = calcul_entropies_batch_jit(sequences_test)
            
            temps_jit = time.time() - start_time
            print(f"   Temps : {temps_jit:.3f}s")
            
            # Calculer le gain
            if temps_jit > 0:
                gain = temps_standard / temps_jit
                print(f"\n🚀 GAIN PERFORMANCE : {gain:.1f}x plus rapide !")
                
                # Vérifier la précision
                diff_max = np.max(np.abs(np.array(entropies_standard) - entropies_jit))
                print(f"✅ Précision : Différence max = {diff_max:.10f}")
                
                return gain
            else:
                print("⚠️ Temps JIT trop rapide pour mesurer")
                return float('inf')
        else:
            print("\n⚠️ Numba non disponible - pas de test JIT")
            return 1.0
            
    except Exception as e:
        print(f"\n❌ Erreur test JIT : {e}")
        return 1.0


def test_chargement_optimise():
    """Test du chargement optimisé du dataset"""
    
    print(f"\n💾 TEST CHARGEMENT OPTIMISÉ")
    print("=" * 35)
    
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé : {dataset_path}")
        return False
    
    # Taille du fichier
    size_gb = os.path.getsize(dataset_path) / (1024**3)
    print(f"📊 Taille dataset : {size_gb:.2f} GB")
    
    # Test chargement avec optimisations
    try:
        from analyseur_transitions_index5 import AnalyseurTransitionsIndex5
        
        print("\n🔄 Test chargement avec optimisations...")
        start_time = time.time()
        
        analyseur = AnalyseurTransitionsIndex5(dataset_path)
        
        temps_init = time.time() - start_time
        print(f"✅ Initialisation : {temps_init:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur chargement : {e}")
        return False


def test_analyse_optimisee_limitee():
    """Test d'analyse optimisée sur un échantillon limité"""
    
    print(f"\n🧪 TEST ANALYSE OPTIMISÉE LIMITÉE")
    print("=" * 40)
    
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé")
        return False
    
    try:
        from analyseur_transitions_index5 import AnalyseurEntropiqueIntegre
        
        print("🎯 Test avec 5 parties...")
        start_time = time.time()
        
        analyseur = AnalyseurEntropiqueIntegre(dataset_path)
        resultats = analyseur.analyser_dataset_complet(nb_parties_max=5)
        
        temps_total = time.time() - start_time
        
        if 'erreur' not in resultats:
            print(f"✅ Analyse réussie en {temps_total:.2f}s")
            
            stats = resultats['statistiques_globales']
            print(f"📊 Parties analysées : {resultats['nb_parties_analysees']}")
            print(f"🎯 Prédictions : {stats['total_predictions_globales']}")
            print(f"✅ Taux succès : {stats['taux_succes_global']:.2f}%")
            
            # Calculer la performance par partie
            temps_par_partie = temps_total / resultats['nb_parties_analysees']
            print(f"⚡ Temps par partie : {temps_par_partie:.2f}s")
            
            # Extrapolation pour 100,000 parties
            temps_100k = temps_par_partie * 100000 / 3600  # en heures
            print(f"📈 Estimation 100,000 parties : {temps_100k:.1f}h")
            
            return temps_par_partie
        else:
            print(f"❌ Erreur analyse : {resultats['erreur']}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur test : {e}")
        import traceback
        traceback.print_exc()
        return None


def installer_optimisations_manquantes():
    """Suggère l'installation des optimisations manquantes"""
    
    print(f"\n🔧 INSTALLATION DES OPTIMISATIONS")
    print("=" * 40)
    
    optimisations = test_optimisations_disponibles()
    
    commandes_install = []
    
    if not optimisations.get('numba', False):
        commandes_install.append("pip install numba")
        print("📦 Numba manquant - JIT compilation désactivée")
    
    if not optimisations.get('orjson', False):
        commandes_install.append("pip install orjson")
        print("📦 orjson manquant - parsing JSON lent")
    
    if commandes_install:
        print(f"\n💡 COMMANDES D'INSTALLATION RECOMMANDÉES :")
        for cmd in commandes_install:
            print(f"   {cmd}")
        
        print(f"\n🚀 Après installation, relancer le test pour gains maximaux !")
    else:
        print(f"\n✅ Toutes les optimisations sont disponibles !")
    
    return len(commandes_install) == 0


def estimer_gains_theoriques():
    """Estime les gains théoriques avec toutes les optimisations"""
    
    print(f"\n📈 ESTIMATION DES GAINS THÉORIQUES")
    print("=" * 45)
    
    # Performance actuelle observée
    temps_actuel_3_parties = 102.77  # secondes
    temps_par_partie_actuel = temps_actuel_3_parties / 3
    
    print(f"📊 Performance actuelle :")
    print(f"   3 parties : {temps_actuel_3_parties}s")
    print(f"   Par partie : {temps_par_partie_actuel:.2f}s")
    
    # Gains estimés par optimisation
    gains = {
        'Numba JIT': 20,      # 10-50x observé dans la littérature
        'Vectorisation': 10,   # 5-20x pour calculs NumPy
        'Memory mapping': 3,   # 2-5x pour I/O
        'Multiprocessing': 6   # 6-8x sur 8 cœurs
    }
    
    print(f"\n🚀 Gains estimés par optimisation :")
    gain_total = 1
    for opt, gain in gains.items():
        gain_total *= gain
        print(f"   {opt:15s} : {gain:2d}x")
    
    print(f"\n⚡ GAIN TOTAL ESTIMÉ : {gain_total:,}x")
    
    # Performance cible
    temps_par_partie_optimise = temps_par_partie_actuel / gain_total
    temps_100k_optimise = temps_par_partie_optimise * 100000 / 60  # minutes
    
    print(f"\n🎯 PERFORMANCE CIBLE :")
    print(f"   Par partie : {temps_par_partie_optimise:.4f}s")
    print(f"   100,000 parties : {temps_100k_optimise:.1f} minutes")
    
    if temps_100k_optimise < 60:
        print(f"   🔥 OBJECTIF : Moins d'1 heure pour 100,000 parties !")
    
    return gain_total


if __name__ == "__main__":
    print("🚀 TEST DES OPTIMISATIONS RÉVOLUTIONNAIRES")
    print("=" * 60)
    
    # 1. Vérifier les optimisations disponibles
    toutes_optimisations = installer_optimisations_manquantes()
    
    # 2. Test performance entropie JIT
    gain_jit = test_performance_entropie_jit()
    
    # 3. Test chargement optimisé
    chargement_ok = test_chargement_optimise()
    
    # 4. Test analyse optimisée
    temps_par_partie = test_analyse_optimisee_limitee()
    
    # 5. Estimation des gains théoriques
    gain_theorique = estimer_gains_theoriques()
    
    # Résumé final
    print(f"\n🎉 RÉSUMÉ DES TESTS")
    print("=" * 25)
    
    if gain_jit > 1:
        print(f"✅ JIT Performance : {gain_jit:.1f}x plus rapide")
    else:
        print(f"⚠️ JIT Performance : Non testé")
    
    if chargement_ok:
        print(f"✅ Chargement optimisé : Fonctionnel")
    else:
        print(f"❌ Chargement optimisé : Problème")
    
    if temps_par_partie:
        print(f"✅ Analyse optimisée : {temps_par_partie:.2f}s/partie")
        
        # Comparaison avec performance actuelle
        temps_actuel = 102.77 / 3
        if temps_par_partie < temps_actuel:
            amelioration = temps_actuel / temps_par_partie
            print(f"🚀 Amélioration réelle : {amelioration:.1f}x")
        else:
            print(f"⚠️ Performance similaire à l'actuelle")
    else:
        print(f"❌ Analyse optimisée : Échec")
    
    print(f"\n🎯 PROCHAINE ÉTAPE : Lancer l'analyse complète optimisée !")
